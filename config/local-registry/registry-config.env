# Registry Configuration for Record Ranger ML Pipeline
# Generated on Tue Jul  8 13:51:24 -04 2025

# External access configuration (requires port forwarding)
REGISTRY_HOST=localhost
REGISTRY_PORT=5000
REGISTRY_URL=localhost:5000

# Internal cluster configuration
REGISTRY_CLUSTER_IP=*************
REGISTRY_SERVICE_PORT=80
REGISTRY_INTERNAL_URL=registry.kube-system.svc.cluster.local:80

# Minikube details
MINIKUBE_IP=************
CLUSTER_NAME=record-ranger-local

# Port forwarding command
PORT_FORWARD_COMMAND="kubectl port-forward --namespace kube-system service/registry 5000:80"
