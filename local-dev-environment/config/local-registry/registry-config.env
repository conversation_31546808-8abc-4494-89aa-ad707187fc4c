# Registry Configuration for Record Ranger ML Pipeline
# Generated on Tue Jul  8 21:38:12 -04 2025

# Primary access configuration
REGISTRY_HOST=************
REGISTRY_PORT=5000
REGISTRY_URL=************:5000
REGISTRY_ACCESS_METHOD=direct

# Internal cluster configuration
REGISTRY_CLUSTER_IP=*************
REGISTRY_SERVICE_PORT=80
REGISTRY_INTERNAL_URL=registry.kube-system.svc.cluster.local:80

# Minikube details
MINIKUBE_IP=************
CLUSTER_NAME=record-ranger-local

# Registry proxy details (if available)
REGISTRY_PROXY_PORT=5000

# Alternative access methods
REGISTRY_MINIKUBE_URL=************:5000
REGISTRY_LOCALHOST_URL=localhost:5000

# Port forwarding command (for localhost access)
PORT_FORWARD_COMMAND="kubectl port-forward --namespace kube-system service/registry 5000:80"
