# Record Ranger ML Pipeline - Local Development Environment Setup Guide

> **🎯 Goal**: Set up a complete local development environment for the Record Ranger ML Pipeline in approximately 30 minutes with automatic registry detection and reliable build processes.

## 📑 Table of Contents

- [Prerequisites](#-prerequisites)
- [Quick Start (30-Minute Setup)](#-quick-start-30-minute-setup)
- [Detailed Setup Process](#-detailed-setup-process)
  - [1. Initial Setup](#1-initial-setup-5-10-minutes)
  - [2. Build Process](#2-build-process-10-15-minutes)
  - [3. Environment Startup](#3-environment-startup-5-8-minutes)
  - [4. Validation Steps](#4-validation-steps-3-5-minutes)
- [Environment Management](#-environment-management)
  - [Environment Termination](#environment-termination)
  - [Daily Operations](#daily-operations)
- [Troubleshooting](#-troubleshooting)
- [Quick Reference](#-quick-reference)
- [Architecture Overview](#-architecture-overview)

---

## 📋 Prerequisites

### Required Tools

Ensure the following tools are installed before proceeding:

| Tool | Minimum Version | Verification Command | Expected Output |
|------|----------------|---------------------|-----------------|
| **Docker** | Latest | `docker --version` | `Docker version 20.x.x` |
| **Minikube** | v1.30+ | `minikube version` | `minikube version: v1.x.x` |
| **kubectl** | v1.28+ | `kubectl version --client` | `Client Version: v1.x.x` |
| **Git** | Latest | `git --version` | `git version 2.x.x` |

### System Requirements

- **RAM**: Minimum 12GB (8GB for Minikube + 4GB for host)
- **CPU**: 4+ cores recommended
- **Disk**: 20GB free space
- **OS**: Linux, macOS, or Windows with WSL2

### Repository Access

Ensure you have access to:
- ✅ `aa_record_ranger_architecture_docs` repository
- ✅ `aa_record_ranger_ml_pipeline` repository (for actual service implementations)

---

## 🚀 Quick Start (30-Minute Setup)

For experienced developers who want to get started immediately:

```bash
# 1. Navigate to project directory
cd /home/<USER>/dev/github/atomadvantage/aa_record_ranger_architecture_docs/local-dev-environment

# 2. Setup Minikube cluster with registry (5-10 min)
./scripts/setup-minikube.sh

# 3. Build all Docker images (10-15 min)
./scripts/build-images.sh

# 4. Deploy all services (5-8 min)
./scripts/start-environment.sh

# 5. Validate environment (3-5 min)
./scripts/validate-registry.sh
./scripts/test-environment.sh
```

**Success Indicators:**
- ✅ All commands complete without errors
- ✅ Registry validation passes
- ✅ All pods show `Running` status
- ✅ Services are accessible via Minikube

---

## 📖 Detailed Setup Process

### 1. Initial Setup (5-10 minutes)

#### Step 1.1: Repository Setup

```bash
# Navigate to the local development environment
cd /home/<USER>/dev/github/atomadvantage/aa_record_ranger_architecture_docs/local-dev-environment

# Verify you're in the correct directory
ls -la scripts/
# Expected: setup-minikube.sh, build-images.sh, validate-registry.sh, etc.
```

#### Step 1.2: Minikube Cluster Creation

```bash
# Create and configure the complete Minikube environment
./scripts/setup-minikube.sh
```

**What this script does:**
- 🏗️ Creates Minikube cluster (`record-ranger-local` profile)
- 🔧 Configures resources (8GB RAM, 4 CPUs, 50GB disk)
- 🔌 Enables addons: `registry`, `ingress`, `dashboard`, `metrics-server`
- 📁 Creates namespaces: `infrastructure`, `ml-pipeline`, `qa-services`
- 🐳 **Automatically detects optimal registry access method**
- 📝 Generates registry configuration with multiple access URLs

**Expected Output:**
```
✅ Minikube setup complete!
📊 Cluster Information:
   Profile: record-ranger-local
   Registry: ************:5000 (direct access)
   # OR: localhost:5000 (port forwarding required)
```

#### Step 1.3: Initial Validation

```bash
# Verify cluster and registry are properly configured
./scripts/validate-registry.sh
```

**Expected Output:**
```
✅ Registry validation completed successfully
💡 Recommendations:
✅ Registry appears to be properly configured and accessible
```

---

### 2. Build Process (10-15 minutes)

#### Step 2.1: Automated Image Building

```bash
# Build all Docker images with automatic registry detection
./scripts/build-images.sh
```

**Enhanced Build Features:**
- 🔍 **Dynamic registry detection** - automatically finds the correct registry URL
- 🔗 **Automatic port forwarding** - sets up registry access when needed
- 🏗️ **Intelligent fallbacks** - creates placeholder images for missing services
- 🧹 **Cleanup management** - properly manages port forwarding processes

**Services Built:**

| Category | Services | Source |
|----------|----------|---------|
| **ML Pipeline** | `downloader`, `classifier`, `splitter`, `metadata-extractor`, `metadata-post-processor`, `validate-route`, `uploader`, `qa-post-processor` | Actual codebase |
| **Placeholder** | `llm-server`, `qa-backend`, `qa-frontend` | Generated placeholders |

#### Step 2.2: Build Verification

```bash
# Verify images were built successfully
docker images | grep -E "(downloader|classifier|splitter|metadata|validate|uploader|qa|llm)"

# Check registry configuration
cat config/local-registry/registry-config.env
```

---

### 3. Environment Startup (5-8 minutes)

#### Step 3.1: Service Deployment

```bash
# Deploy all services in correct dependency order
./scripts/start-environment.sh
```

**Deployment Sequence:**
1. **Infrastructure** (PostgreSQL, RabbitMQ, MinIO)
2. **ML Pipeline Services** (All processing components)
3. **QA Services** (Backend and Frontend)

#### Step 3.2: Deployment Monitoring

```bash
# Watch deployment progress
kubectl get pods --all-namespaces -w

# Check specific namespaces
kubectl get pods -n infrastructure
kubectl get pods -n ml-pipeline  
kubectl get pods -n qa-services
```

**Expected Pod States:**
```
NAMESPACE        NAME                                READY   STATUS    RESTARTS
infrastructure   postgresql-xxx                      1/1     Running   0
infrastructure   rabbitmq-xxx                        1/1     Running   0
infrastructure   minio-xxx                           1/1     Running   0
ml-pipeline      downloader-xxx                      1/1     Running   0
ml-pipeline      classifier-xxx                      1/1     Running   0
# ... (all other services)
```

---

### 4. Validation Steps (3-5 minutes)

#### Step 4.1: Comprehensive System Validation

```bash
# Run complete environment validation
./scripts/test-environment.sh
```

#### Step 4.2: Component-Specific Validation

<details>
<summary><strong>🔍 Registry Validation</strong></summary>

```bash
./scripts/validate-registry.sh
```

**Validation Checks:**
- ✅ Minikube cluster status
- ✅ Registry service availability  
- ✅ Registry pod health
- ✅ Registry access method (direct/port-forwarding)
- ✅ Registry connectivity test

</details>

<details>
<summary><strong>🗄️ Database Validation</strong></summary>

```bash
# Test PostgreSQL connectivity
kubectl exec -n infrastructure deployment/postgresql -- psql -U postgres -d rr -c "\dt"

# Expected: List of database tables
```

</details>

<details>
<summary><strong>🐰 RabbitMQ Validation</strong></summary>

```bash
# Access RabbitMQ Management UI
minikube service rabbitmq-management -n infrastructure
# Credentials: guest/guest

# Check queue status via CLI
kubectl exec -n infrastructure deployment/rabbitmq -- rabbitmqctl list_queues
```

</details>

<details>
<summary><strong>📦 MinIO Validation</strong></summary>

```bash
# Access MinIO Console
minikube service minio-console -n infrastructure  
# Credentials: minioadmin/minioadmin
```

</details>

#### Step 4.3: ML Pipeline Health Checks

```bash
# Test all ML service health endpoints
for service in downloader classifier splitter metadata-extractor metadata-post-processor validate-route uploader qa-post-processor; do
  echo "Testing $service..."
  kubectl exec -n ml-pipeline deployment/$service -- curl -s http://localhost:8080/health || echo "❌ $service not responding"
done
```

#### Step 4.4: Application Access

```bash
# Access QA Frontend
minikube service qa-frontend -n qa-services

# Open Kubernetes Dashboard  
minikube dashboard --profile=record-ranger-local
```

---

## 🛠️ Environment Management

### Environment Termination

#### Graceful Shutdown
```bash
# Stop all services gracefully
./scripts/stop-environment.sh

# Clean up all resources
./scripts/cleanup-environment.sh

# Stop registry port forwarding
./scripts/registry-port-forward.sh stop
```

#### Cluster Management
```bash
# Stop cluster (preserves data)
minikube stop --profile=record-ranger-local

# Delete cluster completely (removes all data)
minikube delete --profile=record-ranger-local
```

### Daily Operations

#### Starting Existing Environment
```bash
# Start stopped cluster
minikube start --profile=record-ranger-local

# Validate registry access
./scripts/validate-registry.sh

# Start services if needed
./scripts/start-environment.sh
```

#### Registry Management
```bash
# Check registry status
./scripts/registry-port-forward.sh status

# Start/stop port forwarding manually
./scripts/registry-port-forward.sh start
./scripts/registry-port-forward.sh stop
```

---

## 🔧 Troubleshooting

### Registry Issues

#### ❌ Problem: Registry not accessible

**Diagnosis:**
```bash
./scripts/validate-registry.sh
```

**Solutions:**
```bash
# Option 1: Restart port forwarding
./scripts/registry-port-forward.sh restart

# Option 2: Regenerate registry config
./scripts/setup-minikube.sh

# Option 3: Manual registry access
curl http://localhost:5000/v2/
curl http://$(minikube ip --profile=record-ranger-local):5000/v2/
```

#### ❌ Problem: Images not building

**Diagnosis:**
```bash
# Check Docker environment
eval $(minikube docker-env --profile=record-ranger-local)
docker images

# Verify ML pipeline repository exists
ls -la /home/<USER>/dev/github/atomadvantage/aa_record_ranger_ml_pipeline/
```

**Solutions:**
```bash
# Rebuild with verbose output
./scripts/build-images.sh

# Manual image build for specific service
docker build -t service-name:latest /path/to/service/
```

### Service Issues

#### ❌ Problem: Pods not starting

**Diagnosis:**
```bash
kubectl get pods --all-namespaces
kubectl describe pod <pod-name> -n <namespace>
kubectl logs <pod-name> -n <namespace>
```

**Common Solutions:**
```bash
# Check resource usage
kubectl top nodes
kubectl top pods --all-namespaces

# Restart deployment
kubectl rollout restart deployment/<service-name> -n <namespace>

# Scale down and up
kubectl scale deployment/<service-name> --replicas=0 -n <namespace>
kubectl scale deployment/<service-name> --replicas=1 -n <namespace>
```

#### ❌ Problem: Services not accessible

**Diagnosis:**
```bash
kubectl get services --all-namespaces
kubectl get endpoints --all-namespaces
kubectl get ingress --all-namespaces
```

**Solutions:**
```bash
# Test internal connectivity
kubectl exec -n <namespace> deployment/<service> -- curl http://<target-service>:8080/health

# Check ingress controller
kubectl get pods -n ingress-nginx

# Restart ingress
kubectl rollout restart deployment/ingress-nginx-controller -n ingress-nginx
```

### Resource Issues

#### ⚠️ Problem: Insufficient resources

**Diagnosis:**
```bash
kubectl top nodes
kubectl describe node minikube
```

**Solutions:**
```bash
# Increase Minikube resources
minikube stop --profile=record-ranger-local
minikube config set memory 12288 --profile=record-ranger-local
minikube config set cpus 6 --profile=record-ranger-local
minikube start --profile=record-ranger-local
```

---

## 📚 Quick Reference

### Essential Commands

| Purpose | Command | Description |
|---------|---------|-------------|
| **Cluster Status** | `minikube status --profile=record-ranger-local` | Check cluster health |
| **Registry Validation** | `./scripts/validate-registry.sh` | Comprehensive registry check |
| **Environment Health** | `./scripts/test-environment.sh` | Full system validation |
| **All Resources** | `kubectl get all --all-namespaces` | View all Kubernetes resources |
| **Service Access** | `minikube service list --profile=record-ranger-local` | List accessible services |

### Port Forwarding for Development

```bash
# PostgreSQL Database
kubectl port-forward -n infrastructure service/postgresql 5432:5432

# RabbitMQ Management UI  
kubectl port-forward -n infrastructure service/rabbitmq-management 15672:15672

# MinIO Console
kubectl port-forward -n infrastructure service/minio-console 9001:9001

# Registry (if needed)
kubectl port-forward -n kube-system service/registry 5000:80
```

### Log Monitoring

```bash
# Follow service logs
kubectl logs -f deployment/<service-name> -n <namespace>

# View recent events
kubectl get events --sort-by=.metadata.creationTimestamp --all-namespaces

# Monitor all pods
kubectl get pods --all-namespaces -w
```

---

## 🏗️ Architecture Overview

### Service Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Minikube Cluster                         │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │  Infrastructure │ │   ML Pipeline   │ │   QA Services   ││
│  │                 │ │                 │ │                 ││
│  │ • PostgreSQL    │ │ • Downloader    │ │ • QA Backend    ││
│  │ • RabbitMQ      │ │ • Classifier    │ │ • QA Frontend   ││
│  │ • MinIO         │ │ • Splitter      │ │                 ││
│  │                 │ │ • Metadata Ext. │ │                 ││
│  │                 │ │ • Validate Route│ │                 ││
│  │                 │ │ • Uploader      │ │                 ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### Registry Access Methods

The enhanced setup automatically detects and configures the optimal registry access method:

1. **Direct Access** (Preferred): `************:5000` via registry proxy
2. **Port Forwarding** (Fallback): `localhost:5000` via kubectl port-forward
3. **Internal Cluster**: `registry.kube-system.svc.cluster.local:80`

### File Structure

```
local-dev-environment/
├── scripts/
│   ├── setup-minikube.sh          # 🔧 Enhanced cluster setup
│   ├── build-images.sh            # 🏗️ Intelligent image building  
│   ├── validate-registry.sh       # ✅ Registry diagnostics
│   ├── registry-port-forward.sh   # 🔗 Port forwarding management
│   ├── start-environment.sh       # 🚀 Service deployment
│   ├── stop-environment.sh        # 🛑 Graceful shutdown
│   ├── cleanup-environment.sh     # 🧹 Resource cleanup
│   └── test-environment.sh        # 🔍 System validation
├── config/
│   └── local-registry/
│       └── registry-config.env    # 📝 Auto-generated registry config
├── k8s/                           # Kubernetes manifests
└── SETUP_GUIDE.md                # 📖 This guide
```

---

## ⏱️ Timeline Summary

| Phase | Duration | Key Activities | Success Criteria |
|-------|----------|----------------|------------------|
| **Prerequisites** | 2-3 min | Tool verification | All tools installed and accessible |
| **Initial Setup** | 5-10 min | Minikube + registry setup | `validate-registry.sh` passes |
| **Build Process** | 10-15 min | Docker image building | All images built successfully |
| **Environment Startup** | 5-8 min | Service deployment | All pods in `Running` state |
| **Validation** | 3-5 min | System verification | All health checks pass |
| **Total** | **25-35 min** | Complete setup | Environment fully operational |

---

## 🎯 Success Criteria

Your environment is ready when:

- ✅ **Registry validation passes**: `./scripts/validate-registry.sh` returns success
- ✅ **All pods are running**: `kubectl get pods --all-namespaces` shows all pods in `Running` state
- ✅ **Services are accessible**: Can access QA frontend, RabbitMQ management, MinIO console
- ✅ **Health checks pass**: `./scripts/test-environment.sh` completes successfully
- ✅ **Database is connected**: Can query PostgreSQL tables
- ✅ **Message queues are active**: RabbitMQ shows expected queues

---

## 📝 Additional Notes

- **First-time setup** may take longer due to Docker image downloads
- **Registry access method** is automatically detected and configured
- **Resource requirements**: Minimum 8GB RAM for Minikube, 12GB total system RAM recommended
- **Data persistence**: Data persists between Minikube restarts unless cluster is deleted
- **Development workflow**: Use `eval $(minikube docker-env)` to build images directly in Minikube's Docker daemon

---

**🚀 Ready to start? Begin with the [Quick Start](#-quick-start-30-minute-setup) section!**
