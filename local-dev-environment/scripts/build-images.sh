#!/bin/bash

# Record Ranger ML Pipeline - Build Images Script
# This script builds all Docker images for local development

set -e

echo "🐳 Building Record Ranger ML Pipeline Docker Images"
echo "=================================================="

# Configuration
CLUSTER_NAME="record-ranger-local"
REGISTRY_CONFIG="config/local-registry/registry-config.env"
ML_PIPELINE_REPO="/home/<USER>/dev/github/atomadvantage/aa_record_ranger_ml_pipeline"
PORT_FORWARD_PID_FILE="/tmp/registry-port-forward.pid"

# Function to detect registry service details dynamically
detect_registry_config() {
    echo "🔍 Detecting registry configuration..."

    # Check if registry service exists
    if ! kubectl get service registry --namespace kube-system >/dev/null 2>&1; then
        echo "❌ Registry service not found in kube-system namespace."
        echo "   Please ensure Minikube registry addon is enabled."
        return 1
    fi

    # Get registry service details
    local registry_cluster_ip=$(kubectl get service registry --namespace kube-system -o jsonpath='{.spec.clusterIP}')
    local registry_port=$(kubectl get service registry --namespace kube-system -o jsonpath='{.spec.ports[0].port}')

    echo "📋 Registry Service Details:"
    echo "   Cluster IP: $registry_cluster_ip"
    echo "   Port: $registry_port"
    echo "   Type: ClusterIP (requires port forwarding for external access)"

    # Set registry configuration
    REGISTRY_HOST="localhost"
    REGISTRY_PORT="5000"
    REGISTRY_URL="$REGISTRY_HOST:$REGISTRY_PORT"
    REGISTRY_CLUSTER_IP="$registry_cluster_ip"
    REGISTRY_SERVICE_PORT="$registry_port"

    return 0
}

# Function to check if registry is accessible
check_registry_access() {
    curl -s "http://$REGISTRY_URL/v2/" >/dev/null 2>&1
}

# Function to start port forwarding if needed
ensure_registry_access() {
    echo "🔗 Ensuring registry access..."

    # Check if registry is already accessible
    if check_registry_access; then
        echo "✅ Registry is already accessible at $REGISTRY_URL"
        return 0
    fi

    echo "🔗 Registry not accessible, setting up port forwarding..."

    # Check if port forwarding is already running
    if [ -f "$PORT_FORWARD_PID_FILE" ]; then
        local pid=$(cat "$PORT_FORWARD_PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo "⚠️  Port forwarding process exists but registry not accessible, restarting..."
            kill "$pid" 2>/dev/null
            rm -f "$PORT_FORWARD_PID_FILE"
        else
            rm -f "$PORT_FORWARD_PID_FILE"
        fi
    fi

    # Start port forwarding
    echo "   Starting port forwarding: kubectl port-forward --namespace kube-system service/registry $REGISTRY_PORT:$REGISTRY_SERVICE_PORT"
    kubectl port-forward --namespace kube-system service/registry $REGISTRY_PORT:$REGISTRY_SERVICE_PORT &
    local pid=$!
    echo $pid > "$PORT_FORWARD_PID_FILE"

    # Wait for registry to be accessible
    echo "⏳ Waiting for registry to be accessible..."
    for i in {1..30}; do
        if check_registry_access; then
            echo "✅ Registry is now accessible at $REGISTRY_URL"
            return 0
        fi
        sleep 1
    done

    echo "❌ Failed to access registry after 30 seconds"
    # Clean up failed port forwarding
    if [ -f "$PORT_FORWARD_PID_FILE" ]; then
        local pid=$(cat "$PORT_FORWARD_PID_FILE")
        kill "$pid" 2>/dev/null
        rm -f "$PORT_FORWARD_PID_FILE"
    fi
    return 1
}

# Load or detect registry configuration
if [ -f "$REGISTRY_CONFIG" ]; then
    echo "📝 Loading registry configuration from $REGISTRY_CONFIG"
    source "$REGISTRY_CONFIG"
    echo "   Registry URL: $REGISTRY_URL"

    # Validate the configuration by detecting actual service
    if ! detect_registry_config; then
        echo "❌ Failed to detect registry service. Please run setup-minikube.sh first."
        exit 1
    fi
else
    echo "📝 Registry configuration file not found, detecting dynamically..."
    if ! detect_registry_config; then
        echo "❌ Failed to detect registry configuration. Please run setup-minikube.sh first."
        exit 1
    fi

    # Create registry configuration for future use
    echo "💾 Saving registry configuration to $REGISTRY_CONFIG"
    mkdir -p "$(dirname "$REGISTRY_CONFIG")"
    cat > "$REGISTRY_CONFIG" << EOF
REGISTRY_HOST=$REGISTRY_HOST
REGISTRY_PORT=$REGISTRY_PORT
REGISTRY_URL=$REGISTRY_URL
EOF
fi

# Check if Minikube is running
echo "🔍 Checking Minikube status..."
if ! minikube status --profile=$CLUSTER_NAME >/dev/null 2>&1; then
    echo "❌ Minikube cluster '$CLUSTER_NAME' is not running."
    echo "   Please run './setup-minikube.sh' first."
    exit 1
fi

# Ensure registry access is available
if ! ensure_registry_access; then
    echo "❌ Failed to establish registry access."
    echo "   Images will be built in Minikube's Docker daemon but won't be pushed to registry."
    echo "   This is sufficient for local development."
    REGISTRY_ACCESSIBLE=false
else
    REGISTRY_ACCESSIBLE=true
fi

# Set Docker environment to use Minikube's Docker daemon
echo "🔧 Setting Docker environment..."
eval $(minikube docker-env --profile=$CLUSTER_NAME)

# Check if ML pipeline repository exists
if [ ! -d "$ML_PIPELINE_REPO" ]; then
    echo "❌ ML Pipeline repository not found at: $ML_PIPELINE_REPO"
    echo "   Please ensure the aa_record_ranger_ml_pipeline repository is cloned."
    exit 1
fi

# Define service mappings: local-dev-name -> actual-repo-directory
declare -A SERVICE_MAPPINGS=(
    ["downloader"]="downloader"
    ["classifier"]="classifier"
    ["splitter"]="splitter"
    ["metadata-extractor"]="metadata_extractor"
    ["metadata-post-processor"]="metadata_postprocessor"
    ["validate-route"]="validate_and_route"
    ["uploader"]="uploader"
    ["qa-post-processor"]="qa_post_processor"
)

# Services that don't have implementations in the ML pipeline repo
declare -a PLACEHOLDER_SERVICES=(
    "llm-server"
    "qa-backend"
    "qa-frontend"
)

# Build each service
echo "🏗️  Building Docker images..."

# Function to create fallback service images
create_fallback_service_image() {
    local service_name="$1"
    local repo_dir="$2"

    docker build -t "$service_name:latest" - << EOF
FROM python:3.10-slim
WORKDIR /app
RUN pip install --no-cache-dir fastapi uvicorn pika psycopg2-binary minio sqlalchemy
RUN echo 'from fastapi import FastAPI
import sys
app = FastAPI()

@app.get("/health")
def health():
    return {"status": "healthy", "service": "$service_name", "type": "fallback"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080)' > main.py

CMD ["python", "main.py"]
EOF
}

# Build services with actual implementations
for service in "${!SERVICE_MAPPINGS[@]}"; do
    repo_dir="${SERVICE_MAPPINGS[$service]}"
    dockerfile_path="$ML_PIPELINE_REPO/$repo_dir/Dockerfile"

    echo "   Building $service from $repo_dir..."

    if [ -f "$dockerfile_path" ]; then
        # Create a local-friendly Dockerfile by replacing private ECR images with public ones
        temp_dockerfile=$(mktemp)

        # Read the original Dockerfile and replace ECR images with public equivalents
        sed 's|112623991000\.dkr\.ecr\.us-east-2\.amazonaws\.com/python:3\.10\.15|python:3.10.15|g;
             s|112623991000\.dkr\.ecr\.us-east-2\.amazonaws\.com/paddle-gpu:2\.6\.1-gpu-cuda11\.7-cudnn8\.4-trt8\.4|python:3.10.15|g' \
             "$dockerfile_path" > "$temp_dockerfile"

        # Build using the modified Dockerfile from the ML pipeline repo root
        docker build -t "$service:latest" -f "$temp_dockerfile" "$ML_PIPELINE_REPO/" || {
            echo "⚠️  Failed to build $service from actual Dockerfile, creating fallback..."
            # Create a simple fallback image
            create_fallback_service_image "$service" "$repo_dir"
        }

        # Clean up temp file
        rm -f "$temp_dockerfile"
    else
        echo "⚠️  Dockerfile not found at $dockerfile_path, creating fallback..."
        create_fallback_service_image "$service" "$repo_dir"
    fi

    # Tag for local registry
    docker tag "$service:latest" "localhost:5000/$service:latest"
    echo "   ✅ $service built successfully"
done



# Build placeholder services
for service in "${PLACEHOLDER_SERVICES[@]}"; do
    echo "   Building placeholder $service..."

    # Create appropriate placeholder based on service type
    if [[ "$service" == "llm-server" ]]; then
        # LLM server placeholder with basic ML dependencies
        docker build -t "$service:latest" - << 'EOF'
FROM python:3.10-slim
WORKDIR /app
RUN pip install --no-cache-dir fastapi uvicorn
RUN echo 'from fastapi import FastAPI
import uvicorn

app = FastAPI()

@app.get("/health")
def health():
    return {"status": "healthy", "service": "llm-server"}

@app.post("/generate")
def generate(prompt: dict):
    return {"response": "LLM placeholder response"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080)' > main.py
EXPOSE 8080
CMD ["python", "main.py"]
EOF
    elif [[ "$service" == "qa-backend" ]]; then
        # QA backend placeholder
        docker build -t "$service:latest" - << 'EOF'
FROM python:3.10-slim
WORKDIR /app
RUN pip install --no-cache-dir fastapi uvicorn sqlalchemy psycopg2-binary
RUN echo 'from fastapi import FastAPI
import uvicorn

app = FastAPI()

@app.get("/health")
def health():
    return {"status": "healthy", "service": "qa-backend"}

@app.get("/api/documents")
def get_documents():
    return {"documents": []}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)' > main.py
EXPOSE 8000
CMD ["python", "main.py"]
EOF
    elif [[ "$service" == "qa-frontend" ]]; then
        # QA frontend placeholder
        docker build -t "$service:latest" - << 'EOF'
FROM node:16-alpine
WORKDIR /app
RUN echo '{"name": "qa-frontend", "version": "1.0.0", "scripts": {"start": "node server.js"}}' > package.json
RUN echo 'const http = require("http");
const server = http.createServer((req, res) => {
  res.writeHead(200, {"Content-Type": "text/html"});
  res.end("<h1>QA Frontend Placeholder</h1><p>Service is running</p>");
});
server.listen(3000, () => console.log("QA Frontend running on port 3000"));' > server.js
EXPOSE 3000
CMD ["npm", "start"]
EOF
    fi

    # Tag for local registry
    docker tag "$service:latest" "localhost:5000/$service:latest"
    echo "   ✅ $service placeholder built successfully"
done

# Push images to local registry (optional)
echo ""
echo "ℹ️  Images are already available in Minikube's Docker daemon."
echo "   For local development, Kubernetes can use these images directly."
echo ""

if [ "$REGISTRY_ACCESSIBLE" = true ]; then
    read -p "📤 Push images to local registry? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "📤 Pushing images to registry..."

        # Verify registry is still accessible
        if ! check_registry_access; then
            echo "⚠️  Registry access lost, re-establishing..."
            if ! ensure_registry_access; then
                echo "❌ Failed to re-establish registry access, skipping push"
                REGISTRY_ACCESSIBLE=false
            fi
        fi

        if [ "$REGISTRY_ACCESSIBLE" = true ]; then
            # Push images
            for service in "${!SERVICE_MAPPINGS[@]}" "${PLACEHOLDER_SERVICES[@]}"; do
                echo "   Pushing $service..."
                if docker push "$REGISTRY_URL/$service:latest"; then
                    echo "   ✅ $service pushed successfully"
                else
                    echo "   ⚠️  Failed to push $service"
                fi
            done
        fi
    else
        echo "⏭️  Skipping registry push. Images are available in Minikube's Docker daemon."
    fi
else
    echo "⚠️  Registry not accessible, skipping push option."
    echo "   Images are available in Minikube's Docker daemon for local development."
fi

# Cleanup function
cleanup() {
    echo "🧹 Cleaning up..."
    if [ -f "$PORT_FORWARD_PID_FILE" ]; then
        local pid=$(cat "$PORT_FORWARD_PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo "   Stopping port forwarding (PID: $pid)..."
            kill "$pid" 2>/dev/null
        fi
        rm -f "$PORT_FORWARD_PID_FILE"
    fi
}

# Set up cleanup trap
trap cleanup EXIT

echo "✅ Image building completed!"
echo ""
echo "📋 Built Images:"
echo "ML Pipeline Services (from actual codebase):"
for service in "${!SERVICE_MAPPINGS[@]}"; do
    echo "   - $service:latest (from ${SERVICE_MAPPINGS[$service]})"
done
echo ""
echo "Placeholder Services:"
for service in "${PLACEHOLDER_SERVICES[@]}"; do
    echo "   - $service:latest (placeholder)"
done
echo ""
if [ "$REGISTRY_ACCESSIBLE" = true ]; then
    echo "📡 Registry Status: ✅ Accessible at $REGISTRY_URL"
else
    echo "📡 Registry Status: ⚠️  Not accessible (images available in Minikube Docker daemon)"
fi
echo ""
echo "🎯 Next Steps:"
echo "   1. Start the environment: ./start-environment.sh"
echo "   2. Check image status: docker images"
echo "   3. Manage registry access: ./scripts/registry-port-forward.sh {start|stop|status}"
echo "   4. Validate registry setup: ./scripts/validate-registry.sh"
echo ""
